import { createUserWithEmailAndPassword, signInWithEmailAndPassword, UserCredential, sendEmailVerification } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { auth, db } from '../firebase';

export async function registerWithEmail(email: string, password: string): Promise<UserCredential> {
  // Nejprve vytvoříme uživatele
  const userCredential = await createUserWithEmailAndPassword(auth, email, password);
  // Počkej, až bude uživatel skutečně p<PERSON>á<PERSON>ený (refresh)
  await new Promise((resolve) => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user && user.email === email) {
        unsubscribe();
        resolve(null);
      }
    });
  });
  // Poté po<PERSON> ov<PERSON>ovac<PERSON> email
  if (userCredential.user) {
    await sendEmailVerification(userCredential.user);
  }
  return userCredential;
}

export async function loginWithEmail(email: string, password: string): Promise<UserCredential> {
  return signInWithEmailAndPassword(auth, email, password);
}

export async function hasUserProfile(userId: string): Promise<boolean> {
  try {
    console.log('Kontroluji profil uživatele (auth.ts):', userId);
    const userDocRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();
      const hasOnboarded = userData.onboarded === true;
      console.log('Uživatel má profil (auth.ts):', hasOnboarded);
      return hasOnboarded;
    }

    console.log('Dokument uživatele neexistuje (auth.ts)');
    return false;
  } catch (error) {
    console.error('Chyba při kontrole profilu (auth.ts):', error);
    return false;
  }
}
