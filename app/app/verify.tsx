import React, { useState } from 'react';
import { View, Text, Button, StyleSheet, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { auth } from '../firebase';
import { sendEmailVerification } from 'firebase/auth';

export default function VerifyScreen() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleCheck = async () => {
    setLoading(true);
    setError('');
    await auth.currentUser?.reload();
    if (auth.currentUser?.emailVerified) {
      // Po ověření emailu přesměrujeme na onboarding pro vytvoření profilu
      router.replace('/onboarding');
    } else {
      setError('Email stále není ověřen. Zkontrolujte schránku a klikněte na ověřovací odkaz.');
    }
    setLoading(false);
  };

  const handleResend = async () => {
    setLoading(true);
    setError('');
    try {
      if (auth.currentUser) {
        await sendEmailVerification(auth.currentUser);
        Alert.alert('Odesláno', 'Ověřovací email byl znovu odeslán.');
      }
    } catch (e: any) {
      setError(e.message || 'Chyba při odesílání emailu');
    }
    setLoading(false);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Ověření emailu</Text>
      <Text style={styles.info}>
        Na váš email byl odeslán ověřovací odkaz. Před pokračováním klikněte na odkaz v emailu.
      </Text>
      {error ? <Text style={styles.error}>{error}</Text> : null}
      <Button title={loading ? 'Kontroluji...' : 'Ověřeno, pokračovat'} onPress={handleCheck} disabled={loading} />
      <Button title="Znovu odeslat ověřovací email" onPress={handleResend} disabled={loading} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 24, backgroundColor: '#fff' },
  title: { fontSize: 24, fontWeight: 'bold', marginBottom: 24, textAlign: 'center' },
  info: { fontSize: 16, color: '#333', marginBottom: 24, textAlign: 'center' },
  error: { color: 'red', marginBottom: 12, textAlign: 'center' },
});