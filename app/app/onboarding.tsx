import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { getAuth } from 'firebase/auth';
import { getFirestore, doc, setDoc } from 'firebase/firestore';
import * as Location from 'expo-location';
import app from '../firebase';
import { Picker } from '@react-native-picker/picker';

const GENDERS = [
  { label: 'Muž', value: 'male', emoji: '👨' },
  { label: '<PERSON>ena', value: 'female', emoji: '👩' },
  { label: 'Jiné', value: 'other', emoji: '⚧️' },
  { label: 'Nechci uvádět', value: '', emoji: '❓' },
];

const AGES = Array.from({ length: 83 }, (_, i) => i + 18); // 18-100

const INTERESTS = [
  { label: 'Pivo', value: 'beer', emoji: '🍺' },
  { label: 'Sport', value: 'sport', emoji: '⚽️' },
  { label: 'Hry', value: 'games', emoji: '🎮' },
  { label: 'Hudba', value: 'music', emoji: '🎵' },
  { label: 'Jídlo', value: 'food', emoji: '🍔' },
];

const PROFILE_EMOJIS = ['🧑', '👩', '👨', '🧔', '👵', '👴', '😎', '🤖', '🐶', '🐱'];

const db = getFirestore(app);
const auth = getAuth(app);

type CustomButtonProps = {
  title: string;
  onPress: () => void;
  color?: string;
  style?: object;
  disabled?: boolean;
};

function CustomButton({ title, onPress, color = '#1fa2ff', style = {}, disabled = false }: CustomButtonProps) {
  return (
    <TouchableOpacity
      onPress={disabled ? undefined : onPress}
      style={[styles.button, { backgroundColor: color, opacity: disabled ? 0.5 : 1 }, style]}
      disabled={disabled}
    >
      <Text style={styles.buttonText}>{title}</Text>
    </TouchableOpacity>
  );
}

export default function OnboardingScreen() {
  const [step, setStep] = useState(0);
  const [nickname, setNickname] = useState('');
  const [gender, setGender] = useState('');
  const [age, setAge] = useState<number | ''>('');
  const [noAge, setNoAge] = useState(false);
  const [interests, setInterests] = useState<string[]>([]);
  const [profileEmoji, setProfileEmoji] = useState(PROFILE_EMOJIS[0]);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const [showAgePicker, setShowAgePicker] = useState(false);

  const handleNext = () => {
    setError('');
    if (step === 0 && !nickname.trim()) {
      setError('Zadejte přezdívku');
      return;
    }
    if (step === 3 && interests.length === 0) {
      setError('Vyberte alespoň jeden zájem');
      return;
    }
    setStep((s) => s + 1);
  };

  const handleBack = () => {
    setError('');
    setStep((s) => s - 1);
  };

  const handleInterestToggle = (value: string) => {
    setInterests((prev) =>
      prev.includes(value) ? prev.filter((i) => i !== value) : [...prev, value]
    );
  };

  const handleFinish = async () => {
    setError('');
    try {
      const user = auth.currentUser;
      if (!user) throw new Error('Uživatel není přihlášen');

      // Nastavíme loading stav, aby uživatel věděl, že se něco děje
      setLoading(true);

      // Uložíme profil do Firestore
      console.log('Ukládám profil uživatele:', user.uid);
      const userDocRef = doc(db, 'users', user.uid);
      console.log('Cesta k dokumentu:', userDocRef.path);

      // Získáme aktuální polohu, pokud je dostupná
      let locationData = null;
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();

        if (status === 'granted') {
          const location = await Location.getCurrentPositionAsync({});
          locationData = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            updatedAt: new Date().toISOString()
          };
        }
      } catch (locationError) {
        console.error('Chyba při získávání polohy:', locationError);
      }

      const userData = {
        nickname,
        email: user.email,  // Přidáme email, aby bylo možné uživatele vyhledat
        gender,
        age: noAge ? null : age,
        interests,
        profileEmoji,
        location: locationData,  // Přidáme polohu, pokud je dostupná
        isVisible: true,  // Výchozí hodnota - uživatel je viditelný
        chatMode: 'ON',   // Výchozí hodnota - Chat ON (LFF)
        createdAt: new Date().toISOString(),
        onboarded: true,
      };

      console.log('Data profilu:', userData);
      await setDoc(userDocRef, userData);
      console.log('Profil úspěšně uložen');

      // Uložíme informaci o profilu do AsyncStorage
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        await AsyncStorage.setItem('userHasProfile', 'true');
        console.log('Informace o profilu uložena do AsyncStorage');
      } catch (storageError) {
        console.error('Chyba při ukládání do AsyncStorage:', storageError);
      }

      // Použijeme replace s parametrem reset, aby se vymazal navigační zásobník
      // a zabránilo se návratu na onboarding
      router.replace('/(tabs)/home?reset=true');
    } catch (e: any) {
      setError(e.message || 'Chyba při ukládání profilu');
      setLoading(false);
    }
  };

  return (
    <ScrollView contentContainerStyle={styles.container} keyboardShouldPersistTaps="handled">
      <Text style={styles.title}>Vytvoření profilu</Text>
      {step === 0 && (
        <>
          <Text style={styles.label}>Přezdívka</Text>
          <TextInput
            style={styles.input}
            value={nickname}
            onChangeText={setNickname}
            placeholder="Zadejte přezdívku"
            placeholderTextColor="#aaa"
          />
          <CustomButton title="Další" onPress={handleNext} />
        </>
      )}
      {step === 1 && (
        <>
          <Text style={styles.label}>Pohlaví</Text>
          <View style={styles.column}>
            {GENDERS.map((g) => (
              <CustomButton
                key={g.value}
                title={`${g.emoji} ${g.label}`}
                onPress={() => setGender(g.value)}
                color={gender === g.value ? '#1fa2ff' : '#eee'}
                style={gender === g.value ? styles.selected : {}}
              />
            ))}
          </View>
          <CustomButton title="Další" onPress={handleNext} disabled={!gender} />
          <CustomButton title="Zpět" onPress={handleBack} color="#aaa" />
        </>
      )}
      {step === 2 && (
        <>
          <Text style={styles.label}>Věk</Text>
          <View style={styles.column}>
            <CustomButton
              title="Nechci uvádět"
              onPress={() => { setNoAge(true); setShowAgePicker(false); }}
              color={noAge ? '#1fa2ff' : '#eee'}
              style={noAge ? styles.selected : {}}
            />
            <CustomButton
              title="Zadat věk"
              onPress={() => { setNoAge(false); setShowAgePicker(true); }}
              color={!noAge && showAgePicker ? '#1fa2ff' : '#eee'}
              style={!noAge && showAgePicker ? styles.selected : {}}
            />
            {showAgePicker && !noAge && (
              <Picker
                selectedValue={age ? String(age) : '18'}
                style={styles.picker}
                onValueChange={(itemValue: string) => setAge(Number(itemValue))}
              >
                {AGES.map((a) => (
                  <Picker.Item key={a} label={String(a)} value={String(a)} />
                ))}
              </Picker>
            )}
          </View>
          <CustomButton title="Další" onPress={handleNext} />
          <CustomButton title="Zpět" onPress={handleBack} color="#aaa" />
        </>
      )}
      {step === 3 && (
        <>
          <Text style={styles.label}>Zájmy</Text>
          <View style={styles.column}>
            {INTERESTS.map((i) => (
              <CustomButton
                key={i.value}
                title={`${i.emoji} ${i.label}`}
                onPress={() => handleInterestToggle(i.value)}
                color={interests.includes(i.value) ? '#1fa2ff' : '#eee'}
                style={interests.includes(i.value) ? styles.selected : {}}
              />
            ))}
          </View>
          <CustomButton title="Další" onPress={handleNext} disabled={interests.length === 0} />
          <CustomButton title="Zpět" onPress={handleBack} color="#aaa" />
        </>
      )}
      {step === 4 && (
        <>
          <Text style={styles.label}>Profilová fotka / Emoji</Text>
          <View style={styles.row}>
            {PROFILE_EMOJIS.map((e) => (
              <TouchableOpacity
                key={e}
                style={[styles.chip, profileEmoji === e && styles.chipSelected]}
                onPress={() => setProfileEmoji(e)}
              >
                <Text style={styles.emoji}>{e}</Text>
              </TouchableOpacity>
            ))}
          </View>
          <CustomButton title={loading ? "Ukládám..." : "Dokončit"} onPress={handleFinish} disabled={loading} />
          <CustomButton title="Zpět" onPress={handleBack} color="#aaa" disabled={loading} />
        </>
      )}
      {error ? <Text style={styles.error}>{error}</Text> : null}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  label: {
    fontSize: 18,
    marginBottom: 8,
    textAlign: 'center',
    color: '#222',
    fontWeight: '600',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    minWidth: 220,
    backgroundColor: '#fff',
    fontSize: 16,
    textAlign: 'center',
    color: '#222',
  },
  column: {
    width: '100%',
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  button: {
    width: '100%',
    paddingVertical: 16,
    borderRadius: 8,
    marginBottom: 12,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18,
  },
  selected: {
    borderWidth: 2,
    borderColor: '#1fa2ff',
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 20,
    gap: 8,
  },
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    margin: 6,
    backgroundColor: '#fff',
  },
  chipSelected: {
    backgroundColor: '#e6f0fa',
    borderColor: '#1fa2ff',
  },
  chipText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#222',
    marginLeft: 6,
  },
  emoji: {
    fontSize: 24,
    marginRight: 2,
  },
  picker: {
    width: 200,
    alignSelf: 'center',
    marginVertical: 8,
  },
  error: {
    color: 'red',
    marginBottom: 16,
    textAlign: 'center',
    fontWeight: 'bold',
  },
});
