import React, { useEffect, useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet, TextInput, Alert } from 'react-native';
import { collection, getDocs, query, where, doc, setDoc, getDoc } from 'firebase/firestore';
import { db, auth } from '../../firebase';
import { useRouter } from 'expo-router';

interface Friend {
  id: string;
  name: string;
  email?: string;
}

export default function FriendsScreen() {
  const [friends, setFriends] = useState<Friend[]>([]);
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const user = auth.currentUser;

  useEffect(() => {
    if (!user) return;
    const fetchFriends = async () => {
      const snapshot = await getDocs(collection(db, 'users', user.uid, 'friends'));
      setFriends(snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Friend)));
    };
    fetchFriends();
  }, [user]);

  const handleChat = (friendId: string) => {
    const chatId = [user?.uid, friendId].sort().join('_');
    router.push({ pathname: './chat', params: { chatId } });
  };

  const handleAddFriend = async () => {
    if (!user || !search.trim()) return;
    setLoading(true);
    try {
      // Hledání podle přezdívky NEBO emailu
      const usersRef = collection(db, 'users');

      // Nejprve zkusíme vyhledat podle přezdívky
      let foundUser = null;
      let snap = await getDocs(query(usersRef, where('nickname', '==', search.trim())));

      // Pokud nenajdeme podle přezdívky, zkusíme email
      if (snap.empty) {
        snap = await getDocs(query(usersRef, where('email', '==', search.trim())));
      }

      // Pokud jsme našli uživatele
      if (!snap.empty) {
        foundUser = snap.docs[0];
      } else {
        Alert.alert('Uživatel nenalezen', 'Zadaný uživatel neexistuje.');
        setLoading(false);
        return;
      }

      // Kontrola, zda se uživatel nepokouší přidat sám sebe
      if (foundUser.id === user.uid) {
        Alert.alert('Nelze přidat sebe', 'Nemůžeš přidat sám sebe mezi přátele.');
        setLoading(false);
        return;
      }

      // Kontrola, zda už je uživatel přidán jako přítel
      const friendDoc = await getDoc(doc(db, 'users', user.uid, 'friends', foundUser.id));
      if (friendDoc.exists()) {
        Alert.alert('Přítel již existuje', 'Tento uživatel je již ve vašem seznamu přátel.');
        setLoading(false);
        return;
      }

      // Přidání do friends kolekce
      const userData = foundUser.data();
      await setDoc(doc(db, 'users', user.uid, 'friends', foundUser.id), {
        name: userData.nickname || userData.email || 'Uživatel',
        email: userData.email || '',
      });

      Alert.alert('Přítel přidán', 'Uživatel byl úspěšně přidán mezi přátele.');
      setSearch('');

      // Refresh seznamu
      const snapshot = await getDocs(collection(db, 'users', user.uid, 'friends'));
      setFriends(snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Friend)));
    } catch (e) {
      console.error('Chyba při přidávání přítele:', e);
      Alert.alert('Chyba', 'Přidání přítele se nezdařilo.');
    }
    setLoading(false);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Moji přátelé</Text>
      <View style={styles.addRow}>
        <TextInput
          style={styles.input}
          value={search}
          onChangeText={setSearch}
          placeholder="Přezdívka nebo email"
          placeholderTextColor="#888"
          editable={!loading}
        />
        <TouchableOpacity style={styles.addButton} onPress={handleAddFriend} disabled={loading}>
          <Text style={styles.addButtonText}>{loading ? 'Přidávám...' : 'Přidat přítele'}</Text>
        </TouchableOpacity>
      </View>
      <FlatList
        data={friends}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <View style={styles.friendRow}>
            <Text style={styles.friendName}>{item.name}</Text>
            <TouchableOpacity style={styles.chatButton} onPress={() => handleChat(item.id)}>
              <Text style={styles.chatButtonText}>Napsat</Text>
            </TouchableOpacity>
          </View>
        )}
        ListEmptyComponent={<Text style={styles.empty}>Nemáš žádné přátele.</Text>}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff', padding: 16 },
  title: { fontSize: 28, fontWeight: 'bold', color: '#1fa2ff', marginBottom: 24, textAlign: 'center' },
  addRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 16 },
  input: { flex: 1, borderWidth: 1, borderColor: '#000', borderRadius: 4, padding: 8, marginRight: 8, color: '#000', backgroundColor: '#fff' },
  addButton: { backgroundColor: '#1fa2ff', paddingVertical: 10, paddingHorizontal: 16, borderRadius: 6 },
  addButtonText: { color: '#fff', fontWeight: 'bold' },
  friendRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingVertical: 12, borderBottomWidth: 1, borderColor: '#eee' },
  friendName: { fontSize: 18, color: '#000' },
  chatButton: { backgroundColor: '#000', paddingVertical: 8, paddingHorizontal: 16, borderRadius: 6 },
  chatButtonText: { color: '#fff', fontWeight: 'bold' },
  empty: { textAlign: 'center', color: '#888', marginTop: 32 },
});