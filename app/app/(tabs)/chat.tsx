import React, { useEffect, useState, useCallback } from 'react';
import { View, TextInput, FlatList, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { collection, addDoc, query, orderBy, onSnapshot, serverTimestamp, Timestamp, doc, getDoc, setDoc } from 'firebase/firestore';
import { db, auth } from '../../firebase';
import { useLocalSearchParams, useRouter } from 'expo-router';

const ONE_HOUR = 60 * 60 * 1000;

// Typ zprávy
interface Message {
  id: string;
  text: string;
  createdAt: Timestamp;
  user: {
    id: string;
    name: string;
  };
}

// Typ pro informace o příteli
interface FriendInfo {
  name: string;
}

export default function ChatScreen() {
  const params = useLocalSearchParams();
  const chatId = params.chatId as string || 'demo-chat';
  const router = useRouter();

  const [messages, setMessages] = useState<Message[]>([]);
  const [text, setText] = useState('');
  const [loading, setLoading] = useState(true);
  const [friendInfo, setFriendInfo] = useState<FriendInfo | null>(null);

  const currentUser = auth.currentUser;

  // Získání informací o příteli
  useEffect(() => {
    const fetchFriendInfo = async () => {
      if (!currentUser || !chatId) return;

      try {
        // Extrahujeme ID přítele z chatId (formát: uid1_uid2)
        const ids = chatId.split('_');
        const friendId = ids[0] === currentUser.uid ? ids[1] : ids[0];

        // Získáme informace o příteli z kolekce friends
        const friendDoc = await getDoc(doc(db, 'users', currentUser.uid, 'friends', friendId));

        if (friendDoc.exists()) {
          setFriendInfo(friendDoc.data() as FriendInfo);
        }
      } catch (error) {
        console.error('Chyba při načítání informací o příteli:', error);
      }
    };

    fetchFriendInfo();
  }, [chatId, currentUser]);

  // Inicializace chatu a načtení zpráv z Firestore
  useEffect(() => {
    if (!chatId || !currentUser) {
      setLoading(false);
      return;
    }

    setLoading(true);

    // Funkce pro inicializaci chatu, pokud ještě neexistuje
    const initializeChat = async () => {
      try {
        // Kontrola, zda chat existuje
        const chatDocRef = doc(db, 'chats', chatId);
        const chatDoc = await getDoc(chatDocRef);

        // Pokud chat neexistuje, vytvoříme ho
        if (!chatDoc.exists()) {
          console.log('Vytvářím nový chat:', chatId);
          await setDoc(chatDocRef, {
            createdAt: serverTimestamp(),
            participants: chatId.split('_'),
            lastMessage: null
          });
        }
      } catch (error) {
        console.error('Chyba při inicializaci chatu:', error);
      }
    };

    // Nejprve inicializujeme chat
    initializeChat().then(() => {
      // Poté nastavíme posluchač pro zprávy
      const unsubscribe = onSnapshot(
        query(
          collection(db, 'chats', chatId, 'messages'),
          orderBy('createdAt', 'desc')
        ),
        (snapshot) => {
          const now = Date.now();
          const msgs = snapshot.docs
            .map(doc => ({ id: doc.id, ...doc.data() } as Message))
            // Filtrujeme zprávy starší než 1 hodina
            .filter(msg => msg.createdAt && (now - msg.createdAt.toDate().getTime() < ONE_HOUR));
          setMessages(msgs);
          setLoading(false);
        },
        (error) => {
          console.error('Chyba při načítání zpráv:', error);
          setLoading(false);
        }
      );

      // Uložíme funkci pro odhlášení posluchače
      return () => unsubscribe();
    });
  }, [chatId, currentUser]);

  // Odeslání zprávy
  const sendMessage = useCallback(async () => {
    if (!currentUser || text.trim().length === 0 || !chatId) return;

    try {
      // Nejprve zkontrolujeme, zda chat existuje
      const chatDocRef = doc(db, 'chats', chatId);
      const chatDoc = await getDoc(chatDocRef);

      // Pokud chat neexistuje, vytvoříme ho
      if (!chatDoc.exists()) {
        console.log('Vytvářím nový chat při odeslání zprávy:', chatId);
        await setDoc(chatDocRef, {
          createdAt: serverTimestamp(),
          participants: chatId.split('_'),
          lastMessage: null
        });
      }

      // Poté přidáme zprávu
      await addDoc(collection(db, 'chats', chatId, 'messages'), {
        text,
        createdAt: serverTimestamp(),
        user: {
          id: currentUser.uid,
          name: currentUser.displayName || 'Uživatel',
        },
      });
      setText('');
    } catch (error) {
      console.error('Chyba při odesílání zprávy:', error);
    }
  }, [text, chatId, currentUser]);

  // Zpět na seznam přátel
  const handleBack = () => {
    router.back();
  };

  return (
    <View style={styles.container}>
      {/* Hlavička chatu */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Zpět</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {friendInfo?.name || 'Chat'}
        </Text>
        <View style={styles.spacer} />
      </View>

      {/* Načítání */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#1fa2ff" />
        </View>
      ) : (
        /* Seznam zpráv */
        <FlatList
          data={messages}
          inverted
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <View style={item.user.id === currentUser?.uid ? styles.myMsg : styles.otherMsg}>
              <Text style={item.user.id === currentUser?.uid ? styles.myMsgText : styles.otherMsgText}>
                {item.text}
              </Text>
              <Text style={styles.msgMeta}>
                {item.createdAt && new Date(item.createdAt.toDate()).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
              </Text>
            </View>
          )}
          ListEmptyComponent={
            <Text style={styles.emptyText}>Žádné zprávy. Napište první zprávu!</Text>
          }
        />
      )}

      {/* Vstupní pole pro zprávu */}
      <View style={styles.inputRow}>
        <TextInput
          style={styles.input}
          value={text}
          onChangeText={setText}
          placeholder="Napiš zprávu..."
          placeholderTextColor="#888"
          multiline
        />
        <TouchableOpacity
          style={[styles.sendButton, !text.trim() && styles.sendButtonDisabled]}
          onPress={sendMessage}
          disabled={!text.trim()}
        >
          <Text style={styles.sendButtonText}>Odeslat</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    backgroundColor: '#fff',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
    flex: 1,
    textAlign: 'center',
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    color: '#1fa2ff',
    fontSize: 16,
  },
  spacer: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputRow: {
    flexDirection: 'row',
    padding: 8,
    borderTopWidth: 1,
    borderColor: '#eee',
    backgroundColor: '#fff',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 20,
    padding: 12,
    paddingTop: 12,
    marginRight: 8,
    color: '#000',
    backgroundColor: '#fff',
    maxHeight: 100,
  },
  sendButton: {
    backgroundColor: '#1fa2ff',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  sendButtonDisabled: {
    backgroundColor: '#ccc',
  },
  sendButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  myMsg: {
    alignSelf: 'flex-end',
    backgroundColor: '#1fa2ff',
    padding: 12,
    borderRadius: 18,
    borderBottomRightRadius: 4,
    margin: 4,
    maxWidth: '80%',
  },
  otherMsg: {
    alignSelf: 'flex-start',
    backgroundColor: '#f1f1f1',
    padding: 12,
    borderRadius: 18,
    borderBottomLeftRadius: 4,
    margin: 4,
    maxWidth: '80%',
  },
  myMsgText: {
    color: '#fff'
  },
  otherMsgText: {
    color: '#000'
  },
  msgMeta: {
    fontSize: 10,
    color: '#888',
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  emptyText: {
    textAlign: 'center',
    color: '#888',
    marginTop: 40,
    padding: 20,
  },
});
