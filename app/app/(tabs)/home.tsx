import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Text, TouchableOpacity, Alert } from 'react-native';
import MapView, { Marker } from 'react-native-maps';
import * as Location from 'expo-location';
import { auth, db } from '../../firebase';
import { collection, doc, setDoc, getDoc, updateDoc, onSnapshot } from 'firebase/firestore';
import { createTestUsers, deleteTestUsers } from '../../utils/testUsers';

// Typ pro uživatele na mapě
interface MapUser {
  id: string;
  nickname: string;
  profileEmoji: string;
  location: {
    latitude: number;
    longitude: number;
  };
  interests: string[];
  chatMode: 'ON' | 'OFF'; // Chat ON (LFF) nebo Chat OFF (RFF)
}

export default function HomeScreen() {
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [users, setUsers] = useState<MapUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isVisible, setIsVisible] = useState(true);
  const [chatMode, setChatMode] = useState<'ON' | 'OFF'>('ON');
  const [userStateLoaded, setUserStateLoaded] = useState(false);
  const currentUser = auth.currentUser;

  // Load user's current state from Firestore
  useEffect(() => {
    const loadUserState = async () => {
      if (!currentUser) return;

      try {
        console.log('Načítám stav uživatele z Firestore...');
        const userDocRef = doc(db, 'users', currentUser.uid);
        const userDoc = await getDoc(userDocRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();
          setIsVisible(userData.isVisible !== false); // Default to true if not set
          setChatMode(userData.chatMode || 'ON'); // Default to ON if not set
          console.log('Stav uživatele načten:', {
            isVisible: userData.isVisible,
            chatMode: userData.chatMode
          });
        } else {
          console.log('Dokument uživatele neexistuje, používám výchozí hodnoty');
        }
      } catch (error) {
        console.error('Chyba při načítání stavu uživatele:', error);
      } finally {
        setUserStateLoaded(true);
      }
    };

    loadUserState();
  }, [currentUser]);

  // Získání a sledování polohy uživatele
  useEffect(() => {
    // Wait for user state to be loaded before setting up location
    if (!userStateLoaded || !currentUser) return;

    let locationSubscription: Location.LocationSubscription | null = null;

    const setupLocation = async () => {
      try {
        console.log('Nastavuji sledování polohy...');

        // Požádáme o povolení přístupu k poloze
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          setErrorMsg('Přístup k poloze byl zamítnut. Aplikace potřebuje přístup k poloze pro zobrazení na mapě.');
          setIsLoading(false);
          return;
        }

        console.log('Povolení k poloze uděleno, získávám aktuální polohu...');

        // Nejprve získáme aktuální polohu
        const initialLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Highest
        });

        console.log('Získána počáteční poloha:',
          initialLocation.coords.latitude,
          initialLocation.coords.longitude
        );

        setLocation(initialLocation);

        // Uložíme počáteční polohu do Firestore
        await saveLocationToFirestore(initialLocation);

        // Nastavíme sledování polohy
        locationSubscription = await Location.watchPositionAsync(
          {
            accuracy: Location.Accuracy.Balanced,
            distanceInterval: 10, // Aktualizace každých 10 metrů
            timeInterval: 30000   // Nebo každých 30 sekund
          },
          (newLocation) => {
            console.log('Aktualizace polohy:',
              newLocation.coords.latitude,
              newLocation.coords.longitude
            );

            setLocation(newLocation);
            saveLocationToFirestore(newLocation);
          }
        );

        console.log('Sledování polohy nastaveno');
      } catch (error) {
        console.error('Chyba při nastavování sledování polohy:', error);
        setErrorMsg('Chyba při získávání polohy. Zkuste restartovat aplikaci nebo zkontrolovat nastavení polohy.');
        setIsLoading(false);
      }
    };

    // Funkce pro uložení polohy do Firestore
    const saveLocationToFirestore = async (locationData: Location.LocationObject) => {
      if (!currentUser) return;

      try {
        const userDocRef = doc(db, 'users', currentUser.uid);

        // Nejprve zkontrolujeme, zda uživatel existuje
        const userDoc = await getDoc(userDocRef);

        if (userDoc.exists()) {
          // Aktualizujeme polohu uživatele
          await updateDoc(userDocRef, {
            location: {
              latitude: locationData.coords.latitude,
              longitude: locationData.coords.longitude,
              updatedAt: new Date().toISOString()
            },
            // Zachováme stávající hodnoty pro viditelnost a chat mód
            isVisible: isVisible,
            chatMode: chatMode
          });
          console.log('Poloha uživatele aktualizována v Firestore');
        }
      } catch (error) {
        console.error('Chyba při ukládání polohy do Firestore:', error);
      }
    };

    // Spustíme nastavení polohy
    setupLocation();

    // Cleanup funkce - zrušíme sledování polohy při unmount
    return () => {
      if (locationSubscription) {
        console.log('Rušíme sledování polohy');
        locationSubscription.remove();
      }
    };
  }, [currentUser, userStateLoaded]); // Only depend on currentUser and userStateLoaded

  // Načtení uživatelů z Firestore
  useEffect(() => {
    // Wait for user state to be loaded before setting up users listener
    if (!userStateLoaded || !currentUser) {
      setIsLoading(false);
      return;
    }

    let unsubscribe: (() => void) | null = null;

    const setupUsersListener = async () => {
      try {
        console.log('Nastavuji posluchač pro uživatele...');
        setIsLoading(true);

        // Získáme všechny uživatele
        const usersRef = collection(db, 'users');

        // Nastavíme posluchač pro změny v kolekci uživatelů
        unsubscribe = onSnapshot(
          usersRef,
          (snapshot) => {
            console.log(`Firestore snapshot obsahuje ${snapshot.size} dokumentů`);
            const mapUsers: MapUser[] = [];

            snapshot.forEach((doc) => {
              // Přeskočíme aktuálního uživatele
              if (doc.id === currentUser.uid) {
                console.log('Přeskakuji aktuálního uživatele');
                return;
              }

              const userData = doc.data();
              console.log(`Zpracovávám uživatele ${doc.id}:`, {
                nickname: userData.nickname,
                hasLocation: !!userData.location,
                isVisible: userData.isVisible,
                chatMode: userData.chatMode
              });

              // Přidáme uživatele pouze pokud má polohu a je viditelný
              if (userData.location &&
                  userData.location.latitude &&
                  userData.location.longitude &&
                  userData.isVisible !== false) { // Pokud isVisible není explicitně false

                console.log('Nalezen viditelný uživatel:', userData.nickname || 'Uživatel');

                // Vypočítáme, jak staré jsou údaje o poloze
                const updatedAt = userData.location.updatedAt
                  ? new Date(userData.location.updatedAt).getTime()
                  : Date.now();

                const ageInMinutes = (Date.now() - updatedAt) / (1000 * 60);

                // Přidáme uživatele pouze pokud jeho poloha není starší než 30 minut
                if (ageInMinutes <= 30) {
                  mapUsers.push({
                    id: doc.id,
                    nickname: userData.nickname || 'Uživatel',
                    profileEmoji: userData.profileEmoji || '👤',
                    location: {
                      latitude: userData.location.latitude,
                      longitude: userData.location.longitude
                    },
                    interests: userData.interests || [],
                    chatMode: userData.chatMode || 'ON'
                  });
                  console.log(`Přidán uživatel ${userData.nickname} na mapu`);
                } else {
                  console.log('Uživatel má zastaralou polohu:', ageInMinutes.toFixed(1), 'minut');
                }
              } else {
                console.log('Uživatel není viditelný nebo nemá polohu:', {
                  hasLocation: !!userData.location,
                  isVisible: userData.isVisible
                });
              }
            });

            console.log(`Načteno ${mapUsers.length} aktivních uživatelů pro zobrazení na mapě`);
            setUsers(mapUsers);
            setIsLoading(false);
          },
          (error) => {
            console.error('Chyba při sledování uživatelů:', error);
            setErrorMsg('Chyba při načítání uživatelů z databáze');
            setIsLoading(false);
          }
        );

        console.log('Posluchač pro uživatele nastaven');
      } catch (error) {
        console.error('Chyba při nastavování posluchače pro uživatele:', error);
        setErrorMsg('Chyba při nastavování sledování uživatelů');
        setIsLoading(false);
      }
    };

    setupUsersListener();

    // Cleanup funkce - zrušíme posluchač při unmount
    return () => {
      if (unsubscribe) {
        console.log('Rušíme posluchač pro uživatele');
        unsubscribe();
      }
    };
  }, [currentUser, userStateLoaded]); // Only depend on currentUser and userStateLoaded

  // Zobrazení informací o uživateli
  const handleMarkerPress = (user: MapUser) => {
    // Základní informace o uživateli
    const userInfo = `Zájmy: ${user.interests.join(', ')}\nChat mód: ${user.chatMode === 'ON' ? 'Chat ON (LFF)' : 'Chat OFF (RFF)'}`;

    // Vytvoříme pole tlačítek pro Alert
    if (user.chatMode === 'ON') {
      // Pokud je uživatel v módu Chat ON, zobrazíme možnost přidat ho jako přítele
      Alert.alert(
        `${user.profileEmoji} ${user.nickname}`,
        userInfo,
        [
          { text: 'Zavřít', style: 'cancel' },
          { text: 'Přidat přítele', onPress: () => handleAddFriend(user.id) }
        ]
      );
    } else {
      // Pokud je uživatel v módu Chat OFF, zobrazíme pouze informace
      Alert.alert(
        `${user.profileEmoji} ${user.nickname}`,
        userInfo,
        [{ text: 'Zavřít', style: 'cancel' }]
      );
    }
  };

  // Přidání uživatele jako přítele
  const handleAddFriend = async (userId: string) => {
    if (!currentUser) return;

    try {
      // Nejprve zkontrolujeme, zda už je uživatel přidán jako přítel
      const friendDoc = await getDoc(doc(db, 'users', currentUser.uid, 'friends', userId));

      if (friendDoc.exists()) {
        Alert.alert('Přítel již existuje', 'Tento uživatel je již ve vašem seznamu přátel.');
        return;
      }

      // Získáme informace o uživateli
      const userDoc = await getDoc(doc(db, 'users', userId));

      if (!userDoc.exists()) {
        Alert.alert('Uživatel nenalezen', 'Tento uživatel již neexistuje.');
        return;
      }

      const userData = userDoc.data();

      // Přidáme uživatele do přátel
      await setDoc(doc(db, 'users', currentUser.uid, 'friends', userId), {
        name: userData.nickname || userData.email || 'Uživatel',
        email: userData.email || '',
      });

      Alert.alert('Přítel přidán', 'Uživatel byl úspěšně přidán mezi přátele.');
    } catch (error) {
      console.error('Chyba při přidávání přítele:', error);
      Alert.alert('Chyba', 'Přidání přítele se nezdařilo.');
    }
  };

  // Aktualizace polohy uživatele
  const handleUpdateLocation = async () => {
    try {
      // Získáme aktuální polohu s vysokou přesností
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Highest
      });

      console.log('Manuální aktualizace polohy:',
        location.coords.latitude,
        location.coords.longitude
      );

      setLocation(location);

      if (currentUser) {
        const userDocRef = doc(db, 'users', currentUser.uid);

        // Aktualizujeme polohu v Firestore
        await updateDoc(userDocRef, {
          location: {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            updatedAt: new Date().toISOString()
          },
          // Zachováme stávající hodnoty pro viditelnost a chat mód
          isVisible: isVisible,
          chatMode: chatMode
        });

        Alert.alert('Poloha aktualizována', 'Vaše poloha byla úspěšně aktualizována a sdílena s ostatními uživateli.');
      }
    } catch (error) {
      console.error('Chyba při aktualizaci polohy:', error);
      Alert.alert('Chyba', 'Aktualizace polohy se nezdařila. Zkuste to prosím znovu.');
    }
  };

  // Přepnutí viditelnosti
  const toggleVisibility = async () => {
    try {
      const newVisibility = !isVisible;
      setIsVisible(newVisibility);

      if (currentUser) {
        const userDocRef = doc(db, 'users', currentUser.uid);

        await updateDoc(userDocRef, {
          isVisible: newVisibility
        });

        Alert.alert(
          newVisibility ? 'Jste viditelní' : 'Jste neviditelní',
          newVisibility
            ? 'Ostatní uživatelé vás nyní vidí na mapě.'
            : 'Ostatní uživatelé vás nyní nevidí na mapě.'
        );
      }
    } catch (error) {
      console.error('Chyba při změně viditelnosti:', error);
      Alert.alert('Chyba', 'Změna viditelnosti se nezdařila.');
    }
  };

  // Přepnutí chat módu
  const toggleChatMode = async () => {
    try {
      const newChatMode = chatMode === 'ON' ? 'OFF' : 'ON';
      setChatMode(newChatMode);

      if (currentUser) {
        const userDocRef = doc(db, 'users', currentUser.uid);

        await updateDoc(userDocRef, {
          chatMode: newChatMode
        });

        Alert.alert(
          newChatMode === 'ON' ? 'Chat ON (LFF)' : 'Chat OFF (RFF)',
          newChatMode === 'ON'
            ? 'Ostatní uživatelé vás mohou kontaktovat.'
            : 'Ostatní uživatelé vás nemohou kontaktovat.'
        );
      }
    } catch (error) {
      console.error('Chyba při změně chat módu:', error);
      Alert.alert('Chyba', 'Změna chat módu se nezdařila.');
    }
  };

  // Test functions for development
  const handleCreateTestUsers = async () => {
    Alert.alert(
      'Vytvořit testovací uživatele?',
      'Toto vytvoří 3 testovací uživatele na mapě pro testování.',
      [
        { text: 'Zrušit', style: 'cancel' },
        {
          text: 'Vytvořit',
          onPress: async () => {
            const success = await createTestUsers();
            Alert.alert(
              success ? 'Úspěch' : 'Chyba',
              success
                ? 'Testovací uživatelé byli vytvořeni. Měli byste je vidět na mapě.'
                : 'Nepodařilo se vytvořit testovací uživatele.'
            );
          }
        }
      ]
    );
  };

  const handleDeleteTestUsers = async () => {
    Alert.alert(
      'Smazat testovací uživatele?',
      'Toto smaže všechny testovací uživatele z mapy.',
      [
        { text: 'Zrušit', style: 'cancel' },
        {
          text: 'Smazat',
          onPress: async () => {
            const success = await deleteTestUsers();
            Alert.alert(
              success ? 'Úspěch' : 'Chyba',
              success
                ? 'Testovací uživatelé byli smazáni.'
                : 'Nepodařilo se smazat testovací uživatele.'
            );
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      {errorMsg ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{errorMsg}</Text>
        </View>
      ) : isLoading ? (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Načítání mapy...</Text>
        </View>
      ) : (
        <>
          <MapView
            style={styles.map}
            initialRegion={{
              latitude: location?.coords.latitude || 50.0755,
              longitude: location?.coords.longitude || 14.4378,
              latitudeDelta: 0.09,
              longitudeDelta: 0.04,
            }}
            showsUserLocation={true}
            showsMyLocationButton={true}
            showsCompass={true}
            showsScale={true}
          >
            {/* Marker pro aktuálního uživatele */}
            {location && (
              <Marker
                coordinate={{
                  latitude: location.coords.latitude,
                  longitude: location.coords.longitude,
                }}
                title="Já"
                description="Moje poloha"
                pinColor="blue"
              />
            )}

            {/* Markery pro ostatní uživatele */}
            {users.length > 0 ? (
              users.map((user) => (
                <Marker
                  key={user.id}
                  coordinate={{
                    latitude: user.location.latitude,
                    longitude: user.location.longitude,
                  }}
                  title={`${user.profileEmoji} ${user.nickname}`}
                  description={`Zájmy: ${user.interests.join(', ')}`}
                  onPress={() => handleMarkerPress(user)}
                />
              ))
            ) : (
              // Pokud nejsou žádní uživatelé, zobrazíme pouze aktuálního uživatele
              <Text style={{ display: 'none' }}>Žádní uživatelé k zobrazení</Text>
            )}
          </MapView>

          {/* Debugovací informace */}
          <View style={styles.debugContainer}>
            <Text style={styles.debugText}>
              🌍 Poloha: {location ? `${location.coords.latitude.toFixed(4)}, ${location.coords.longitude.toFixed(4)}` : 'Neznámá'}
            </Text>
            <Text style={styles.debugText}>
              👥 Uživatelé na mapě: {users.length}
            </Text>
            <Text style={styles.debugText}>
              👁️ Viditelnost: {isVisible ? 'Viditelný' : 'Neviditelný'}
            </Text>
            <Text style={styles.debugText}>
              💬 Chat mód: {chatMode === 'ON' ? 'Chat ON (LFF)' : 'Chat OFF (RFF)'}
            </Text>
            <Text style={styles.debugText}>
              🔄 Stav načten: {userStateLoaded ? 'Ano' : 'Ne'}
            </Text>
            <Text style={styles.debugText}>
              👤 Uživatel: {currentUser?.email || 'Nepřihlášen'}
            </Text>
            <Text style={styles.debugText}>
              ⏰ Poslední aktualizace: {location ? new Date(location.timestamp).toLocaleTimeString() : 'Neznámá'}
            </Text>
          </View>

          {/* Ovládací panel */}
          <View style={styles.controlPanel}>
            <TouchableOpacity
              style={[styles.controlButton, styles.locationButton]}
              onPress={handleUpdateLocation}
            >
              <Text style={styles.buttonText}>📍 Aktualizovat polohu</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.controlButton, isVisible ? styles.visibleButton : styles.invisibleButton]}
              onPress={toggleVisibility}
            >
              <Text style={styles.buttonText}>
                {isVisible ? '👁️ Viditelný' : '👁️‍🗨️ Neviditelný'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.controlButton, chatMode === 'ON' ? styles.chatOnButton : styles.chatOffButton]}
              onPress={toggleChatMode}
            >
              <Text style={styles.buttonText}>
                {chatMode === 'ON' ? '💬 Chat ON (LFF)' : '🔕 Chat OFF (RFF)'}
              </Text>
            </TouchableOpacity>

            {/* Test user controls - only show in development */}
            <TouchableOpacity
              style={[styles.controlButton, styles.testButton]}
              onPress={handleCreateTestUsers}
            >
              <Text style={styles.buttonText}>🧪 Vytvořit test uživatele</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.controlButton, styles.testButton]}
              onPress={handleDeleteTestUsers}
            >
              <Text style={styles.buttonText}>🗑️ Smazat test uživatele</Text>
            </TouchableOpacity>
          </View>
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  },
  map: {
    flex: 1
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
  },
  // Debugovací informace
  debugContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 10,
    borderRadius: 10,
    zIndex: 1000,
  },
  debugText: {
    color: 'white',
    fontSize: 14,
    marginBottom: 5,
  },
  // Ovládací panel
  controlPanel: {
    position: 'absolute',
    bottom: 100, // Výrazně zvýšíme vzdálenost od spodního okraje
    left: 20,
    right: 20,
    flexDirection: 'column',
    gap: 12, // Zvětšíme mezeru mezi tlačítky
    zIndex: 1000, // Zajistíme, že bude nad mapou
    elevation: 5, // Pro Android
    backgroundColor: 'rgba(255, 255, 255, 0.1)', // Přidáme lehké pozadí
    padding: 10, // Přidáme padding
    borderRadius: 15, // Zaoblíme rohy
  },
  controlButton: {
    paddingVertical: 16, // Ještě více zvětšíme výšku tlačítka
    paddingHorizontal: 20,
    borderRadius: 25,
    elevation: 8, // Zvýšíme elevaci pro lepší viditelnost
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 5,
    alignItems: 'center',
    marginBottom: 10, // Zvětšíme mezeru mezi tlačítky
    borderWidth: 2, // Zvýrazníme okraj
    borderColor: 'rgba(255,255,255,0.5)', // Světlejší okraj pro lepší viditelnost
    backgroundColor: 'rgba(0, 0, 0, 0.7)', // Tmavší pozadí pro lepší kontrast
  },
  locationButton: {
    backgroundColor: 'rgba(31, 162, 255, 0.9)', // Světle modrá s průhledností
  },
  visibleButton: {
    backgroundColor: 'rgba(76, 175, 80, 0.9)', // Zelená s průhledností
  },
  invisibleButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.9)', // Červená s průhledností
  },
  chatOnButton: {
    backgroundColor: 'rgba(33, 150, 243, 0.9)', // Modrá s průhledností
  },
  chatOffButton: {
    backgroundColor: 'rgba(255, 152, 0, 0.9)', // Oranžová s průhledností
  },
  testButton: {
    backgroundColor: 'rgba(156, 39, 176, 0.9)', // Fialová s průhledností pro test tlačítka
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18, // Zvětšíme písmo
    textShadowColor: 'rgba(0, 0, 0, 0.8)', // Tmavší stín textu pro lepší čitelnost
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
    letterSpacing: 0.5, // Přidáme mezery mezi písmeny
  },
  // Ponecháváme pro zpětnou kompatibilitu
  updateButton: {
    display: 'none', // Skryjeme, protože už ho nepoužíváme
  },
  updateButtonText: {
    display: 'none', // Skryjeme, protože už ho nepoužíváme
  },
});
