import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { signOut } from 'firebase/auth';
import { auth } from '../../firebase';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function SettingsScreen() {
  const router = useRouter();

  const handleSignOut = async () => {
    try {
      // Nejprve vymažeme informaci o profilu z AsyncStorage
      try {
        await AsyncStorage.removeItem('userHasProfile');
        console.log('Informace o profilu odstraněna z AsyncStorage');
      } catch (storageError) {
        console.error('Chyba při mazání z AsyncStorage:', storageError);
      }

      // Poté odhlásíme uživatele
      await signOut(auth);
      console.log('Uživatel byl odhlášen');

      // Přesměrujeme na login obrazovku
      router.push('/login');
    } catch (e) {
      console.error('Chyba při odh<PERSON>:', e);
      Alert.alert('Chyba', 'Odhlášení se nezdařilo.');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Nastavení</Text>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Profil</Text>
        <Text style={styles.sectionDesc}>Zobrazit a upravit informace o profilu</Text>
      </View>
      <TouchableOpacity style={styles.friendsButton} onPress={() => router.push('./friends')}>
        <Text style={styles.friendsText}>Moji přátelé</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.logoutButton} onPress={handleSignOut}>
        <Text style={styles.logoutText}>Odhlásit se</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1fa2ff',
    marginBottom: 32,
    textAlign: 'center',
  },
  section: {
    marginBottom: 32,
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#f5f7fa',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#222',
  },
  sectionDesc: {
    fontSize: 16,
    color: '#555',
  },
  logoutButton: {
    marginTop: 16,
    backgroundColor: '#eee',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  logoutText: {
    color: '#d32f2f',
    fontWeight: 'bold',
    fontSize: 18,
  },
  friendsButton: {
    marginTop: 16,
    backgroundColor: '#1fa2ff',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  friendsText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18,
  },
});
