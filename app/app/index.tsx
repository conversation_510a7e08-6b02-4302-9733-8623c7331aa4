import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import { useEffect, useState } from 'react';

// Tato komponenta slouží jako vstupní bod aplikace
// Přesměrování je řešeno v _layout.tsx na základě stavu autentizace a profilu
export default function Index() {
  const [loadingText, setLoadingText] = useState('Načítání...');

  // Simulace načítání s měnícím se textem
  useEffect(() => {
    const loadingMessages = [
      'Načítání aplikace...',
      'Kontrola přihlášení...',
      'Připravujeme vše pro vás...',
      'Téměř hotovo...'
    ];

    let currentIndex = 0;
    const interval = setInterval(() => {
      currentIndex = (currentIndex + 1) % loadingMessages.length;
      setLoadingText(loadingMessages[currentIndex]);
    }, 1500);

    return () => clearInterval(interval);
  }, []);

  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color="#1fa2ff" style={styles.spinner} />
      <Text style={styles.loadingText}>{loadingText}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  spinner: {
    marginBottom: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#555',
    textAlign: 'center',
  }
});
