import React from 'react';
import { useEffect, useState } from 'react';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { Stack, useRouter, usePathname } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';
import { useColorScheme } from '@/hooks/useColorScheme';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth, db } from '../firebase';
import { useFonts } from 'expo-font';
import { ActivityIndicator, View, Text } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { doc, getDoc } from 'firebase/firestore';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

function LoadingScreen() {
  const [loadingText, setLoadingText] = useState('Načítání...');

  // Simulace načítání s měnícím se textem
  useEffect(() => {
    const loadingMessages = [
      'Načítání aplikace...',
      'Kontrola přihlášení...',
      'Připravujeme vše pro vás...',
      'Téměř hotovo...'
    ];

    let currentIndex = 0;
    const interval = setInterval(() => {
      currentIndex = (currentIndex + 1) % loadingMessages.length;
      setLoadingText(loadingMessages[currentIndex]);
    }, 1500);

    return () => clearInterval(interval);
  }, []);

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' }}>
      <ActivityIndicator size="large" color="#1fa2ff" style={{ marginBottom: 20 }} />
      <Text style={{ fontSize: 16, color: '#555', textAlign: 'center' }}>{loadingText}</Text>
    </View>
  );
}

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });
  const [authChecked, setAuthChecked] = useState(false);
  const [profileChecked, setProfileChecked] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [hasProfile, setHasProfile] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // Kontrola přihlášení uživatele
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
      setAuthChecked(true);
    });
    return unsubscribe;
  }, []);

  // Kontrola, zda má uživatel vytvořený profil
  useEffect(() => {
    if (!user) {
      setProfileChecked(true);
      setHasProfile(false);
      return;
    }

    // Pokud jsme na cestě s parametrem reset, předpokládáme, že uživatel má profil
    // Toto je důležité pro přesměrování z onboardingu na home
    if (pathname.includes('reset=true')) {
      console.log('Cesta obsahuje reset=true, předpokládáme, že uživatel má profil');
      setHasProfile(true);
      setProfileChecked(true);
      return;
    }

    // Pokud jsme již na home screen, předpokládáme, že uživatel má profil
    if (pathname.includes('(tabs)/home')) {
      console.log('Jsme již na home screen, předpokládáme, že uživatel má profil');
      setHasProfile(true);
      setProfileChecked(true);
      return;
    }

    // Pokud jsme na onboarding screen, ale uživatel má profil, přesměrujeme ho na home
    if (pathname === '/onboarding') {
      console.log('Jsme na onboarding screen, kontrolujeme, zda uživatel má profil');
    }

    const checkUserProfile = async () => {
      try {
        console.log('Kontroluji profil uživatele (_layout.tsx):', user.uid);

        // Přímá kontrola v Firestore místo použití funkce hasUserProfile
        const userDocRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userDocRef);

        console.log('Dokument existuje:', userDoc.exists());

        if (userDoc.exists()) {
          const userData = userDoc.data();
          console.log('Data uživatele:', userData);
          const hasOnboarded = userData.onboarded === true;
          console.log('Uživatel má profil (onboarded):', hasOnboarded);

          // Uložíme informaci o profilu do AsyncStorage pro budoucí použití
          try {
            const AsyncStorage = require('@react-native-async-storage/async-storage').default;
            await AsyncStorage.setItem('userHasProfile', hasOnboarded ? 'true' : 'false');
            console.log('Informace o profilu uložena do AsyncStorage');
          } catch (storageError) {
            console.error('Chyba při ukládání do AsyncStorage:', storageError);
          }

          setHasProfile(hasOnboarded);
          setProfileChecked(true);
          return hasOnboarded;
        } else {
          console.log('Dokument uživatele neexistuje');

          // Uložíme informaci o profilu do AsyncStorage
          try {
            const AsyncStorage = require('@react-native-async-storage/async-storage').default;
            await AsyncStorage.setItem('userHasProfile', 'false');
          } catch (storageError) {
            console.error('Chyba při ukládání do AsyncStorage:', storageError);
          }

          setHasProfile(false);
          setProfileChecked(true);
          return false;
        }
      } catch (error) {
        console.error('Chyba při kontrole profilu (_layout.tsx):', error);
        setHasProfile(false);
        setProfileChecked(true);
        return false;
      }
    };

    // Nejprve zkusíme načíst informaci z AsyncStorage
    const checkStoredProfileStatus = async () => {
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        const storedStatus = await AsyncStorage.getItem('userHasProfile');

        console.log('Načtená informace o profilu z AsyncStorage:', storedStatus);

        if (storedStatus === 'true') {
          console.log('Uživatel má profil podle AsyncStorage');
          setHasProfile(true);
          setProfileChecked(true);

          // I když máme informaci z AsyncStorage, pro jistotu aktualizujeme z Firestore
          // ale nebudeme čekat na dokončení
          checkUserProfile();
        } else {
          // Pokud nemáme informaci nebo je false, zkontrolujeme Firestore
          // a počkáme na dokončení
          await checkUserProfile();
        }
      } catch (error) {
        console.error('Chyba při načítání z AsyncStorage:', error);
        // Pokud selže AsyncStorage, zkontrolujeme Firestore
        await checkUserProfile();
      }
    };

    checkStoredProfileStatus();
  }, [user, pathname]);

  // Přesměrování uživatele na základě stavu autentizace a profilu
  useEffect(() => {
    if (!authChecked || !profileChecked || !loaded) return;

    // Přidáme malé zpoždění, aby se stihly načíst všechny informace
    const redirectTimeout = setTimeout(() => {
      try {
        console.log('Přesměrování - stav uživatele:', {
          pathname,
          hasProfile,
          emailVerified: user?.emailVerified
        });

        // Pokud jsme na cestě s parametrem reset nebo jsme již na home screen, necháme navigaci proběhnout
        if (pathname.includes('reset=true') || pathname.includes('(tabs)/home')) {
          return;
        }

        // Pokud uživatel není přihlášen a není na přihlašovací obrazovce
        if (!user && pathname !== '/login' && pathname !== '/verify' && pathname !== '/onboarding') {
          console.log('Přesměrování na login screen...');
          router.replace('/login');
          return;
        }

        // Pokud je uživatel přihlášen
        if (user) {
          // Pokud není ověřený email a není na obrazovce pro ověření
          if (!user.emailVerified && pathname !== '/verify') {
            console.log('Přesměrování na verify screen...');
            router.replace('/verify');
            return;
          }

          // Pokud je ověřený email a má profil, ale je na onboardingu, přesměrujeme na home
          if (user.emailVerified && hasProfile && pathname === '/onboarding') {
            console.log('Uživatel již má profil, přesměrování z onboardingu na home screen...');
            router.replace('/home');
            return;
          }

          // Pokud je ověřený email, ale nemá profil a není na onboardingu
          if (user.emailVerified && !hasProfile && pathname !== '/onboarding' && !pathname.includes('(tabs)')) {
            console.log('Přesměrování na onboarding screen...');
            router.replace('/onboarding');
            return;
          }

          // Pokud je ověřený email a má profil, ale je na onboardingu, přesměrujeme na home
          if (user.emailVerified && hasProfile && pathname === '/onboarding') {
            console.log('Uživatel již má profil, přesměrování na home screen...');
            router.replace('/home');
            return;
          }

          // Pokud je ověřený email, má profil a je na přihlašovací obrazovce nebo na hlavní stránce
          if (user.emailVerified && hasProfile && (pathname === '/login' || pathname === '/')) {
            console.log('Přesměrování na home screen...');
            router.replace('/home');
            return;
          }
        }
      } catch (error) {
        console.error('Chyba při přesměrování:', error);
      }
    }, 500); // Zpoždění 500 ms

    return () => clearTimeout(redirectTimeout);
  }, [user, authChecked, profileChecked, loaded, pathname]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded || !authChecked || !profileChecked) {
    return (
      <GestureHandlerRootView style={{ flex: 1 }}>
        <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
          <LoadingScreen />
          <StatusBar style="auto" />
        </ThemeProvider>
      </GestureHandlerRootView>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
        <Stack>
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="+not-found" />
          <Stack.Screen name="login" options={{ headerShown: false }} />
        </Stack>
        <StatusBar style="auto" />
      </ThemeProvider>
    </GestureHandlerRootView>
  );
}
