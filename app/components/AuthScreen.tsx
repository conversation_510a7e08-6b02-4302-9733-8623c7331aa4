import React, { useState } from 'react';
import { View, TextInput, Button, Text, StyleSheet } from 'react-native';
import { registerWithEmail, loginWithEmail } from '../services/auth';
import { useRouter } from 'expo-router';
import { auth, db } from '../firebase';
import { doc, getDoc } from 'firebase/firestore';

export default function AuthScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isRegister, setIsRegister] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleAuth = async () => {
    setLoading(true);
    setError('');
    try {
      if (isRegister) {
        await registerWithEmail(email, password);
        // Po registraci přesměruj na verify screen
        router.replace('./verify');
      } else {
        await loginWithEmail(email, password);
        const user = auth.currentUser;
        if (user && !user.emailVerified) {
          setError('Nejprve ověřte svůj email.');
          router.replace('./verify');
          return;
        }

        // Při přihlášení vždy přesměrujeme na home screen
        // Pokud uživatel nemá profil, bude přesměrován na onboarding v _layout.tsx
        router.replace('/(tabs)/home');
      }
    } catch (e: any) {
      setError(e.message || 'Chyba při autentizaci');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{isRegister ? 'Registrace' : 'Přihlášení'}</Text>
      <TextInput
        style={styles.input}
        placeholder="Email"
        autoCapitalize="none"
        keyboardType="email-address"
        value={email}
        onChangeText={setEmail}
      />
      <TextInput
        style={styles.input}
        placeholder="Heslo"
        secureTextEntry
        value={password}
        onChangeText={setPassword}
      />
      {error ? <Text style={styles.error}>{error}</Text> : null}
      <Button
        title={loading ? 'Probíhá...' : isRegister ? 'Registrovat se' : 'Přihlásit se'}
        onPress={handleAuth}
        disabled={loading}
      />
      <Text style={styles.switch} onPress={() => setIsRegister(!isRegister)}>
        {isRegister ? 'Máte účet? Přihlaste se' : 'Nemáte účet? Registrujte se'}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 24,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    textAlign: 'center',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  error: {
    color: 'red',
    marginBottom: 12,
    textAlign: 'center',
  },
  switch: {
    color: '#007bff',
    marginTop: 16,
    textAlign: 'center',
  },
});
