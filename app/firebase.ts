import { initializeApp, getApps, getApp } from 'firebase/app';
import { getAuth, initializeAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { firebaseConfig } from './constants/firebaseConfig';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Inicializace Firebase pouze jednou (Expo hot reload safe)
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();

// Inicializace Firebase Auth
// Poznámka: Pro development build s persistencí je potřeba odkomentovat kód níže
// a zakomentovat standardní inicializaci
let auth: ReturnType<typeof getAuth>;

// Standardní inicializace pro Expo Go
// auth = getAuth(app);

// Inicializace s persistencí pro development build
try {
  // Dynamický import pro React Native persistence
  // Toto funguje pouze v development buildu, ne v Expo Go
  const { getReactNativePersistence } = require('firebase/auth');

  auth = initializeAuth(app, {
    persistence: getReactNativePersistence(AsyncStorage)
  });
  console.log('Firebase Auth inicializován s persistencí');
} catch (error) {
  console.error('Chyba při inicializaci Firebase Auth s persistencí:', error);
  auth = getAuth(app);
}

export { auth };
export const db = getFirestore(app);
export default app;
