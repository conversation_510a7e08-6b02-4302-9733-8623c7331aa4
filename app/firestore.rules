rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Uživatelské profily - ka<PERSON><PERSON><PERSON> můž<PERSON>, ale jen vlastník může upravovat
    match /users/{userId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
      
      // Přátelé - každý může č<PERSON>, ale jen vlastník může upravovat
      match /friends/{friendId} {
        allow read: if true;
        allow write: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // Chaty - jen účastníci chatu mohou číst a zapisovat
    match /chats/{chatId} {
      // Formát chatId: "uid1_uid2" (seřazeno)
      allow read, write: if request.auth != null && 
        (chatId.split("_")[0] == request.auth.uid || 
         chatId.split("_")[1] == request.auth.uid);
      
      // Zprávy v chatu
      match /messages/{messageId} {
        allow read, write: if request.auth != null && 
          (chatId.split("_")[0] == request.auth.uid || 
           chatId.split("_")[1] == request.auth.uid);
      }
    }
  }
}
