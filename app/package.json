{"name": "app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "^5.1.2", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.2.10", "expo": "~52.0.46", "expo-blur": "~14.0.3", "expo-constants": "~17.0.8", "expo-dev-client": "~5.0.20", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-location": "^18.0.10", "expo-router": "~4.0.20", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "firebase": "^11.6.1", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-gifted-chat": "^2.8.1", "react-native-maps": "1.18.0", "react-native-paper": "^5.13.5", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.14", "@types/react": "~18.3.12", "@types/react-native": "^0.72.8", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}