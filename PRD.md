📄 Product Requirements Document (PRD)

📱 Projekt: Instantní Socializační <PERSON> (🧪 pracovní název)

💡 Koncept
Sociální 📲 appka inspirovaná 🚗 Uberem, která umožňuje lidem se stejn<PERSON>mi z<PERSON> (🍻, ⚽️, ❤️) se spojit instantně v jejich okolí. Na základě aktivního nebo pasivního režimu se 👤 uživatelé zobrazují na 🗺️ mapě a mohou se propojit k osobnímu 👥 setkání.

🔁 Hlavní módy:

Looking for Fun (LFF) – 🟢 aktivní mód: uživatel chce být kontaktov<PERSON>.
Ready for Fun (RFF) – 🟡 pasivní mód: uživatel se jen ukazuje na 🗺️ mapě, bez možnosti kontaktu.
🧰 Funkce:

Vý<PERSON><PERSON><PERSON> z<PERSON>j<PERSON> (🍻, ⚽️, ❤️, ...)
Zobrazení ostatních 👥 na 🗺️ pomocí ikon dle zájmu
Kliknutí na 👤: ukázání 🆔 přezdívky, 📷 fotky a 📝 krátkého popisu
Možnost navázání kontaktu přes ⏱️ časově omezený 💬 chat
🔔 Notifikace ve stylu Uberu ("někdo má zájem o 🤝 setkání")
⭐️ Hodnocení po schůzce: uživatelé hodnotí zážitek pomocí slideru (od zamračeného smajlíka po zamilovaného).
👨‍👩‍👧‍👦 Možnost zakládat skupiny (např. hledáme 1️⃣ hráče na ⚽️)
🆓 Možnost přidat si vlastní emoji ikonu a volný popisek na mapu (nezávisle na definovaných zájmech)
📲 Monetizace:
Základní verze: 1 aktivita denně zdarma (v radiusu 5 km).
Premium verze: Neomezený počet aktivit, rozšířený radius (např. až 50 km), a další exkluzivní funkce.
Možnost zakoupení premium verze na měsíční bázi nebo rozšíření radiusu.
Notifikace po schůzce: "Pokud máš zážitek, podpoř nás tipem nebo zakoup Premium!"
🧪 Tech Stack (MVP návrh)
🎨 Frontend:

⚛️ React Native (📲 cross-platform mobilní appka)
🧠 Backend:

🔥 Firebase (Auth, Firestore, Realtime DB, Cloud Messaging)
☁️ Google Cloud Functions (serverless logika pro chat/🔔 notifikace)
🗺️ Mapy:

Google Maps SDK + 🧭 Places API
💬 Chat:

Firebase Firestore + realtime updates nebo Stream.io (alternativa s expirací)
🚀 Hosting / Build:

Expo (📦 rychlý vývoj a testování)
🤖 AI:

GPT nebude zatím součástí appky, slouží jen pro vývoj v AI Agent módu ve VS Code.
🧱 MVP Cíl

🎯 Cíl MVP:
Dostat appku do stavu, kdy:

👥 Uživatelé se zobrazí na ��️ mapě.
Vidí 🎯 zájmy jeden druhého.
Mohou navázat kontakt (🔵 LFF mód).
Spustí se ⏱️ 💬 chat.
🛠️ Instrukce pro vývoj v AI Agent Módu (ChatGPT + VS Code)

📝 Zadání pro agenta:
Vytvoř React Native appku přes Expo.
Připoj Firebase projekt (🔐 auth + 🔥 Firestore + realtime DB).
Implementuj registraci & přihlášení 👤 (🆔 přezdívka, 🎯 zájem, mód, 📷 profilová fotka, 📍 poloha).
Načítej a zobrazuj ostatní 👥 na 🗺️ mapě dle polohy a zájmu (jen LFF viditelní).
Umožni kliknutí na marker ➡️ zobrazit profil + možnost propojení.
Po potvrzení druhé strany spustit 🕒 💬 chat (např. 1h ➡️ smaže se).
Chat přes Firebase Firestore realtime sync.
🔧 Postup:
Inicializace projektu: npx create-expo-app social-app
Instalace knihoven:
firebase
react-native-maps
expo-location
react-navigation
react-native-gifted-chat / stream-chat-react-native
Nastavení Firebase: auth, firestore, rules
Vytvoření obrazovek: 🔑 Login, 🆕 Signup, 🗺️ Map, 🧑‍💼 Profile, 💬 Chat
Logika pro zobrazení LFF na mapě
Realtime sync pozic a stavů uživatelů
UI/UX propojení & spuštění chatu
Pravidla pro automatické 🗑️ mazání chatu po čase
🖼️ Obrazovky aplikace (MVP návrh)

1. 🆕 Signup / 🔑 Login Screen
Pole pro 📧 email
Pole pro 🔑 heslo
Tlačítka: 🔓 Přihlásit se / 📝 Registrovat se
Přesměrování na další krok: 🧑‍💼 Vytvoření profilu (přezdívka, zájmy, mód, fotka)
Možnost přihlášení přes 🔵 Google / 🟦 Facebook (volitelné)
2. 🧑‍💼 Vytvoření profilu
Pole pro 🆔 přezdívku
Nahrání 📷 profilové fotky
Výběr 🎯 zájmů z hierarchické struktury kategorií:
Např. Sport ➡️ Míčové sporty ➡️ 🏀 Basketbal, ⚽️ Fotbal
Připravena struktura pro budoucí snadné přidávání dalších kategorií a podkategorií
Výběr módu: 🔵 Looking for Fun (LFF) / 🟡 Ready for Fun (RFF)
Automatická detekce 📍 polohy nebo ruční zadání
3. 🗺️ Map Screen
Zobrazení uživatelů v okolí s ikonami dle zájmů
Po kliknutí na uživatele: zobrazení přezdívky, fotky a krátkého popisu
Možnost navázání kontaktu v LFF módu (po potvrzení druhé strany se spustí chat)
4. 💬 Chat Screen
Realtime chat
Časově omezený chat (např. 1 hodina, po které se smaže)
Hodnocení zážitku po chatování