# 🧪 Tech Stack (MVP – Kompatibilita a verze)

## 🎨 Frontend: React Native
- **Framework:** [React Native](https://reactnative.dev/)
  - Doporučená verze: `0.74+` (aktuální stabilní)
- **Vývoj:** [Expo](https://expo.dev/)
  - SDK: `SDK 50+` (kompatibilní s RN 0.74)
  - Výhoda: Rychlý prototyping, OTA updates

---

## 🧠 Backend: Firebase
- **Firebase SDK (JS/React Native):**
  - `firebase`: `v10.x` nebo novější
  - Podporované moduly:
    - `firebase/auth`
    - `firebase/firestore`
    - `firebase/database` (Realtime DB – volitelně)
    - `firebase/messaging` (pro notifikace)
- **Google Cloud Functions:**
  - Node.js runtime: `v18` (LTS)
  - Použití: serverless logika (např. notifikace, expirační logika pro chat)
  - Deployment přes Firebase CLI: `v13+`

---

## 🗌 Mapy:
- **Google Maps SDK:**
  - React Native knihovna: `react-native-maps` – doporučená verze `1.6.0+`
  - Platformní závislosti:
    - iOS: vyžaduje CocoaPods + Google Maps API key
    - Android: `play-services-maps` knihovna

- **Google Places API:**
  - REST API nebo SDK pro autocomplete a nearby search
  - Nutné aktivovat v Google Cloud Console

---

## 💬 Chat:
### Varianta A – Firebase Firestore
- Realtime updates pomocí `onSnapshot`
- Volitelně kombinace s Realtime Database pro aktivní indikátory

### Varianta B – Stream.io (alternativa)
- [Stream Chat React Native SDK](https://getstream.io/chat/docs/sdk/reactnative/)
  - Kompatibilní s React Native `0.74+`
  - Expirační zprávy, moderace, push notifikace
  - Vyžaduje backend klíč pro tokeny nebo použití Cloud Functions

---

## 🚀 Hosting & Build: Expo
- **Expo Go** pro vývoj/testování
- **EAS Build** pro produkční buildy
  - iOS a Android distribuce (TestFlight / Google Play)
  - Konfigurace přes `eas.json`

